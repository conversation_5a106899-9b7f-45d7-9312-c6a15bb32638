module Mkp
  module Integration
    class Base < ActiveRecord::Base
      PRODUCT_UPDATABLE_PARAMS = [
        :available_on,
        :regular_price,
        :sale_price,
        :sale_on,
        :sale_until,
        :available_properties,
        :title,
        :description
      ].freeze

      VARIANT_UPDATABLE_PARAMS = [
        :quantity,
        :properties,
        :sku
      ].freeze

      AFTER_REMOVE_OPTIONS = [
        :delete_integrables,
        :remove_integrables_stock,
        :hide_shop,
        :nothing
      ].freeze

      UNSYNCABLE_SHOP_IDS = [788, 797, 817, 818, 822, 585, 798, 863, 922]

      self.abstract_class = true

      self.table_name = 'mkp_integrations'

      belongs_to :shop, class_name: '::Mkp::Shop'
      has_many   :objects,
                 class_name: '::Mkp::Integration::Object',
                 foreign_key: :integration_id,
                 dependent: :destroy

      validates_uniqueness_of :account_name, scope: :type

      serialize :scopes, Array
      serialize :settings, Hash

      def self.for(shop)
        find_by_shop_id_and_type(shop.id, name) || new(shop_id: shop.id, type: name)
      end

      def incomplete?
        access_token.blank?
      end

      def authorized?
        access_token.present? && active?
      end

      def active?
        return true unless expires
        Time.now < expires_at
      end

      def need_custom_configuration?
        false
      end

      def configuration_complete?
        true
      end

      def simple_type
        type.split('::').last.downcase
      end

      def setup_session_params
        raise NotImplementedError
      end

      def process_notification(topic)
        raise NotImplementedError
      end

      # This two fellows here signal if an integration is being cleaned up
      # by an asynchronous process (i.e. in case the user wanted to get rid
      # of their local products, etc.)
      def deleting?
        settings[:deleting]
      end

      def deleting
        settings.merge!(deleting: true)
        save!
      end

      def can_be_deleted?
        !syncing?
      end

      def syncing!
        settings['syncing'] = true
        update_column(:settings, settings)
      end

      def syncing?
        settings['syncing'].present? ? settings['syncing'] : false
      end

      def synced!
        settings.delete('syncing')
        update_column(:settings, settings)
      end

      def import_and_sync
        external_products = integration_product_model.find(:all)
        external_products_ids = external_products.map(&:external_id)

        without_category_list = []
        without_manufacturer_list = []

        # Import/sync products from external source.
        integrated_products = external_products.each_with_object([]) do |external_product, list|
          begin
            if integrated_product_exists?(external_product)
              offer = get_offer_info(external_product.external_id)
              update_related_product(external_product, offer)
              sync_variants(external_product)
            else
              next if external_product.pictures.blank?

              if (external_product.to_hash)[:category_id].blank?
                without_category_list << external_product
                next
              end

              if (external_product.to_hash)[:manufacturer_id].blank?
                without_manufacturer_list << external_product
                next
              end

              list << create_integrated_product(external_product)
            end
          rescue ActiveRecord::RecordInvalid => exception
            handle_exception(exception, external_product.to_hash(true))
          end
        end
        # Handle inactive (integration-dependant definition) products.
        handle_inactive_products(external_products_ids)

        # Notify results of the import/sync asynchronously.
        IntegrationMailer.import_and_sync(self, integrated_products.size).deliver
        IntegrationMailer.products_without_category(self, without_category_list).deliver if without_category_list.present?
        IntegrationMailer.products_without_manufacturer(self, without_manufacturer_list).deliver if without_manufacturer_list.present?

        synced!
        integrated_products
      end

      def delete_integration_dependency(option)
        case option
        when :delete_integrables
          objects.flat_map(&:integrable).map(&:destroy)
        when :remove_integrables_stock
          products = objects.where(integrable_type: Mkp::Product.name).flat_map(&:integrable)

          variants_ids = products.flat_map(&:variants).map(&:id)

          Mkp::Variant.where(id: variants_ids).update_all(quantity: 0)
        when :hide_shop
          shop.update_attributes(visible: false)
        end
      end

      def sync_visibility(integration_object)
        external_product = integration_product_model.find([integration_object.external_id]).first
        integration_object.integrable.update_attributes(available_on: external_product.available_on)
      end

      def create_integration_object(internal_object, external_object, single = false)
        Mkp::Integration::Object.create do |object|
          object.integrable = internal_object
          object.integration_id = id
          object.integration_name = simple_type
          object.external_id = external_object.external_id
          object.single = single
        end
      end

      def job_queued_key(*key_parts)
        key_parts.unshift("working:#{simple_type}:job:queued:").join('/')
      end

      def integrated_product_exists?(external_product)
        integrated_product(external_product).present?
      end

      def update_related_product(external_product, offer = nil)
        product = integrated_product(external_product).integrable
        params_to_update = product_attributes_to_update(product, external_product)
        params_to_update.merge!(offer) if offer.present?
        product.update_attributes(params_to_update) if params_to_update.present?
      end

      def sync_variants(external_product)
        ActiveRecord::Base.transaction do
          product = integrated_product(external_product).integrable

          handle_outdated_variants(product, external_product)

          if external_product.without_variants?
            variant = product.variants.first
            external_variant = external_product.variants.first

            params_to_update = variant_attributes_to_update(variant, external_variant)
            variant.update_attributes(params_to_update) if params_to_update.present?
          else
            external_product.variants.each do |external_variant|
              if (integration_object = objects.find_by_external_id(external_variant.external_id)).present?
                variant = integration_object.integrable

                params_to_update = variant_attributes_to_update(variant, external_variant)

                variant.update_attributes(params_to_update) if params_to_update.present?
              else
                create_integrated_variant(product, external_variant)
              end
            end
          end

          retrieve_missing_pictures(product, external_product)

          recalculate_visibility(product)
        end
      end

      def integration_product_model
        @_model ||= "Lux::Integration::#{self.class.name.demodulize}::Product".constantize.new(self)
      end

      protected

      def session
        # This could be overriden, if the integration needs it.
      end

      def handle_inactive_products(active_external_ids)
        # This should be override on each integration
      end

      def ensure_only_one_at_a_time(*key_parts)
        # We are dealing with this in here because each integration delivers
        # a different payload, so some of them are handle at the entry (WebhookController)
        # and others in here.
        #
        # This is an extra attemp to avoid duplications due to race conditions.
        task_key = key_parts.unshift("working:#{simple_type}").join('/')

        if $redis.setnx(task_key, true)
          yield
        end
      ensure
        $redis.del(task_key)
      end

      def ensure_only_one_job_queued(*key_parts)
        # This is an extra attemp to avoid duplications on jobs due to race conditions.
        task_key = job_queued_key(*key_parts)
        if $redis.setnx(task_key, true)
          yield
        end
      end

      def create_integrated_product(external_product)
        if external_product_is_valid?(external_product)
          unless integrated_product_exists?(external_product)
            ensure_only_one_at_a_time('product', 'create', external_product.external_id) do
              AuditHelper.with_whodunnit('system-job:mkp_integration_base',
                                         'create_integrated_product') do
                ActiveRecord::Base.transaction do
                  begin
                    offer = get_offer_info(external_product.external_id)
                    product_data = external_product.to_hash
                    product_data.merge!(offer) unless offer.blank?
                    product = Mkp::Product.create!(product_data)
                    single = external_product.without_variants?

                    create_integration_object(product, external_product, single)

                    external_product.variants.each do |external_variant|
                      create_integrated_variant(product, external_variant, single: single)
                    end

                    external_product.pictures.each_with_index do |picture, index|
                      process_picture(picture, product.id, index)
                    end

                    recalculate_visibility(product)
                    #create_dummy_packages(product)
                    product
                  rescue => exception
                    handle_exception( exception, external_product.to_hash(true))
                  end
                end
              end
            end
          end
        else
          handle_exception(NotAllowedPropertyException.new, external_product.to_hash(true))
        end
      end

      def create_dummy_packages(product)
        product.packages.create!(hast_to_packages(product))
      end

      def hast_to_packages(product)
        network = product.shop.try(:network) || 'AR'
        {
          mass_unit: Network.for(network).mass_unit,
          length_unit: Network.for(network).length_unit,
          width: 0,
          height: 0,
          length: 0,
          weight: 0
        }
      end

      def create_integrated_variant(product, external_variant, single: false)
        ensure_only_one_at_a_time('variant', 'create', external_variant.external_id) do
          variant = product.variants.create!(external_variant.to_hash)
          create_integration_object(variant, external_variant) unless single
        end
      end

      def retrieve_missing_pictures(product, external_product)
        if (unprocessed_pictures = get_unprocessed_pictures(product, external_product)).present?
          process_external_pictures(unprocessed_pictures, product, external_product)
        end
      end
      def get_unprocessed_pictures(product, external_product)
        external_product.pictures.reject do |external_picture|
          integrated_picture_exist_for_product?(product, external_picture)
        end
      end
      def integrated_picture_exist_for_product?(product, external_picture)
        return false unless (integrated_picture_to_check = integrated_picture(external_picture)).present?

        product.pictures.pluck(:id).any? do |picture_id|
          picture_id == integrated_picture_to_check.integrable_id
        end
      end
      def integrated_picture(external_picture)
        objects.where({
          integrable_type: Mkp::Attachment::Picture.name,
          external_id: external_picture.external_id
        }).first
      end
      def process_external_pictures(unprocessed_pictures, product, external_product)
        unprocessed_pictures.each do |picture|
          view_order = external_product.pictures.index(picture)
          process_picture(picture, product.id, view_order)
        end
      end

      def handle_outdated_variants(product, external_product)
        single = product.external_object.single

        # Local product was single and now the external product have variants.
        if single && external_product.with_variants?
          unmark_as_single(product) and return
        # Local product had variants and now is single.
        elsif (not single) && external_product.without_variants?
          mark_as_single(product, external_product) and return
        end

        if external_product.with_variants?
          variants = product.variants

          stale_external_ids = variants.flat_map(&:external_objects).map(&:external_id).map(&:to_i)
          incoming_external_ids = external_product.variants.map(&:external_id).map(&:to_i)

          # Which ones we have, that doesn't have an external counterpart.
          candidates = stale_external_ids - incoming_external_ids

          old_variants = Mkp::Integration::Object.where(external_id: candidates).map(&:integrable)

          destroy_outdated_variants(old_variants) if old_variants.present?
        end
      ensure
        product.reload
      end

      def unmark_as_single(product)
        product.external_object.update_attributes(single: false)

        destroy_outdated_variants(product.variants)
      end

      def mark_as_single(product, external_product)
        product.external_object.update_attributes(single: true)

        destroy_outdated_variants(product.variants)

        create_integrated_variant(product, external_product.variants.first, single: true)
      end

      def destroy_outdated_variants(variants)
        variants.each do |variant|
          variant.picture && variant.picture.destroy
          variant.destroy
        end
      end

      def delete_integration_product(external_product_id)
        object = Mkp::Integration::Object.where(external_id: external_product_id).first

        if (integrable = object.try(:integrable)).present?
          integrable.destroy
        end
      end

      def integrated_product(external_product, variant_search = false)
        conditions = { external_id: external_product.external_id }
        unless variant_search
          conditions.tap do |options|
            options[:integrable_type] = Mkp::Product.name
          end
        end
        objects.where(conditions).first
      end

      def able_to_display?(external_product)
        return false unless (integration_object = integrated_product(external_product)).present?
        integration_object.display
      end

      def sibling_already_exists?(external_variant)
        return false unless (external_variant_ids = external_variant.picture.variant_ids).present?

        objects.where(
          integrable_type: Mkp::Variant.name,
          external_id: external_variant_ids - [external_variant.external_id]
        ).exists?
      end

      def product_attributes_to_update(product, external_product)
        updatable_params = if able_to_display?(external_product)
          PRODUCT_UPDATABLE_PARAMS
        else
          PRODUCT_UPDATABLE_PARAMS - [:available_on]
        end

        # Warn: Stop syncing price and sale price for UNSYNCABLE SHOP products.
        # if Time.now > Time.parse('Tuesday November 22, 2016 00:00 -03') &&
        #    Time.now < Time.parse('Saturday November 26, 2016 23:59 -03')
          if self.class::UNSYNCABLE_SHOP_IDS.include?(product.shop.id)
            updatable_params = updatable_params - remove_params
          # end
        end

        external_product.to_hash.select do |key, value|
          if [:regular_price, :sale_price].include?(key)
            updatable_params.include?(key) && !value.to_i.zero?
          else
            updatable_params.include?(key) && product[key] != value
          end
        end
      end

      def remove_params
        [
          :regular_price,
          :sale_price,
          :sale_on,
          :sale_until
        ]
      end

      def variant_attributes_to_update(variant, external_variant)
        external_variant.to_hash.select do |key, value|
          self.class::VARIANT_UPDATABLE_PARAMS.include?(key) && variant[key] != value
        end
      end

      def process_picture(external_picture, product_id, order)
        ensure_only_one_job_queued('picture', 'import', external_picture.external_id) do
          external_picture = external_picture.to_hash unless external_picture.is_a?(Hash)
          ProductPictureImporterWorker.perform_async(id, external_picture, product_id, order)
        end
      end

      def external_product_is_valid?(external_product)
        not (external_product.available_properties.include?(:noproperty) &&
          external_product.variants.size > 1)
      end

      def handle_exception(exception, payload)
        data = {
          msg: exception.message,
          payload: {
            data: payload.as_json
          }
        }

        ExceptionNotifier.notify_exception(exception, data: data)

        true
      end

      def recalculate_visibility(product)
        product.delay_for(10.seconds).recreate_variants_visibility
      end

      class NotAllowedPropertyException < StandardError; end
    end
  end
end
