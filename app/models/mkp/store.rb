class Mkp::Store < ActiveRecord::Base
  has_paper_trail meta: {
    custom_event: ->(*_) { (PaperTrail.request.controller_info || {})[:custom_event] }
  }

  serialize :preferred_sorting, Array

  LABELS_GATEWAYS = %w[Lion OcaEpak Atene] + Smartcarrier::Const::CARRIERS
  BNA_STORE_IDS = [41, 43, 47].freeze

  before_create :set_name
  before_create :generate_token
  validates :title, presence: true
  validates :name, uniqueness: true
  validates :percentage_fee, numericality: { only_integer: true }, presence: true
  validates :limit_amount, numericality: { greater_than_or_equal_to: 0 }, presence: true
  validates :visa_puntos_equivalence, numericality: { greater_than: 0 }, allow_nil: true
  validates :visa_puntos_sube_equivalence, numericality: { greater_than: 0 }, allow_nil: true
  validates :visa_puntos_recargas_equivalence, numericality: { greater_than: 0 }, allow_nil: true

  has_many :bines, foreign_key: :store_id, class_name: 'Bin'
  has_many :installments
  has_many :carts, foreign_key: :store_id, class_name: 'Mkp::Cart'
  has_many :coupons, foreign_key: :store_id, class_name: 'Mkp::Coupon'
  has_many :landings, foreign_key: :store_id, class_name: 'Pages::Landing'
  has_many :orders, foreign_key: :store_id, class_name: 'Mkp::Order'
  has_many :menus, foreign_key: :store_id, class_name: 'Mkp::Menu'
  has_many :sitemaps, foreign_key: :store_id
  has_many :category_stores
  has_many :categories, through: :category_stores
  has_many :shop_stores
  has_many :shops, through: :shop_stores
  has_many :active_shops, -> { active_now }, :class_name => 'Mkp::ShopStore'
  has_many :carriers, foreign_key: :store_id, class_name: 'Mkp::CarrierStore', dependent: :destroy
  has_many :customers, foreign_key: :store_id, class_name: 'Mkp::Customer'
  has_many :roles
  has_many :product_stores
  has_one :free_shipping, foreign_key: :store_id, dependent: :destroy
  has_one :decidir_credential
  has_many :first_data_credentials, dependent: :delete_all
  has_one :cancelation_service_configuration, dependent: :destroy
  has_one :whitelist_configuration
  has_one :aerolineas_argentinas_credential
  has_many :gateway_credentials, foreign_key: :store_id, dependent: :destroy
  has_many :exports, foreign_key: :store_id, class_name: 'Mkp::Export'
  has_many :customer_reservation_purchases, class_name: 'CustomerReservationPurchases', dependent: :destroy
  has_many :gateway_errors, foreign_key: :store_id, class_name: 'Mkp::GatewayError', dependent: :destroy
  has_one :renaper_configuration, dependent: :destroy
  has_one :equifax_id_validator_configuration, dependent: :destroy

  enum insurance_source: [:insurance_configuration_table, :pioneer, :ochenta_grados]

  has_many :gateway_alternative_strategies, dependent: :destroy
  has_many :payment_credentials, -> { where(shop_id: nil) }, dependent: :destroy, foreign_key: :store_id

  accepts_nested_attributes_for :installments, allow_destroy: true
  accepts_nested_attributes_for :carriers
  accepts_nested_attributes_for :free_shipping, allow_destroy: true
  accepts_nested_attributes_for :decidir_credential
  accepts_nested_attributes_for :first_data_credentials, reject_if: :all_blank, allow_destroy: true
  accepts_nested_attributes_for :cancelation_service_configuration
  accepts_nested_attributes_for :whitelist_configuration, allow_destroy: true
  accepts_nested_attributes_for :aerolineas_argentinas_credential, allow_destroy: true
  accepts_nested_attributes_for :gateway_credentials, allow_destroy: true
  accepts_nested_attributes_for :renaper_configuration, allow_destroy: true
  accepts_nested_attributes_for :gateway_alternative_strategies, allow_destroy: true, update_only: true

  accepts_nested_attributes_for :payment_credentials, allow_destroy: true, update_only: true
  validates_associated :free_shipping

  scope :active, -> { where(active: true) }

  delegate :generate_refund_shipments, :generate_coupons, :generate_credit_notes, :cancel_payments, to: :cancelation_service_configuration

  PAPERCLIP_CONFIGS = {
    path: 'vnrs/:public_folder/:attachment_:token_:style.:extension'
  }.freeze

  has_attached_file :image_dues,
                    PAPERCLIP_CONFIGS.merge(styles: lambda { |_a|
                                                      { thumbnail: 'x50',
                                                        normal: '500x260#' }
                                                    })
  validates_attachment :image_dues,
                       content_type: { content_type: %w[image/bmp image/gif image/jpeg image/png] },
                       size: { in: 0..2.megabytes }

  def self.options_for_select
    order('LOWER(name)').map { |e| [e.name, e.id] }
  end

  def set_name
    self.name = title.delete(' ').downcase
  end

  def front_name
    title || name.capitalize
  end

  def retrieve_gateways_array
    gateway.split(',')
  end

  def default_carrier
    OpenStruct.new(gateway: 'Krabpack', price: 10006.0, delivery_message: 'Entrega estandar')
  end

  def get_gateway_for_labels(zip_code, weight = nil, shop_id = nil, operative = 'door_to_door')
    carrier = get_carrier(zip_code, weight, shop_id, operative)
    carrier.try(:gateway).capitalize || 'OcaEpak'
  end

  def get_delivery_options(zip_code, weights, ids, operative = 'door_to_door')
    result = []

    ids.map.each_with_index do |id, index|
      carrier = get_carrier(zip_code, weights[index], id, operative)
      result << { carrier: carrier.gateway, price: carrier.price, service: carrier.delivery_message }.with_indifferent_access
    end.compact

    result
  end

  def get_carrier(zip_code, weight = nil, shop_id = nil, operative = 'door_to_door')
    zip = zip_code.gsub(/[^0-9,.]/, '').to_i
    carriers = self.carriers.where(operative: operative).with_zip(zip)
    carriers = carriers.with_weight(weight) if weight.present?
    carriers_by_shop = carriers.with_shop_id(shop_id)
    carriers = carriers_by_shop.empty? ? carriers.with_shop_id(nil) : carriers_by_shop

    if carriers.any?
      carriers.first
    else
      # Determinar el motivo por el cual no hubo match
      reason = if weight.present? && !self.carriers.with_weight(weight).exists?
        "Peso no contemplado en matriz (#{weight})"
      elsif !self.carriers.with_zip(zip).exists?
        "Código postal no contemplado en matriz (#{zip_code})"
      elsif self.carriers.empty?
        "Matriz no cargada"
      else
        "No se encontró match en la matriz"
      end

      shop = Mkp::Shop.find_by(id: shop_id) if shop_id.present?

      message = "🚨 Envío con costo predeterminado\n" \
      "Store: #{self.title}\n" \
      "Shop: #{shop&.name || 'N/A'} (id: #{shop_id})\n" \
      "Motivo: #{reason}"

      RocketChatNotifier.notify(message, webhook_dest: :matrix)

      default_carrier
    end
  end

  def discount(amount)
    amount - (amount * percentage_fee.fdiv(100))
  end

  def system_point_availability?
    retrieve_gateways_array.include?('system_points')
  end

  def decidir_credentials
    decidir_credential || Mkp::Store.find_by(name: 'avenida').decidir_credential
  end

  def mercado_pago_public_key
    gateway_credentials.count == 4 ? gateway_credentials.find_by(name: 'public_key').value : MERCADOPAGO_PUBLIC_KEY
  end

  def has_visa_puntos?
    retrieve_gateways_array.include?('visa_puntos') && visa_puntos_equivalence.present? && visa_puntos_api_key.present?
  end

  def first_data_credential_for_checkout_cart(checkout_cart)
    credentials = []
    checkout_cart.items.each do |hash|
      coll = first_data_credentials.select { |each| each.categories_ids.present? && (each.categories_ids & (Mkp::Variant.find hash[:id]).categories.collect(&:id)).present? }
      if coll.blank?
        credentials << first_data_credentials.select {|each| each.categories_ids.blank?}
      else
        credentials << coll
      end
    end
    credentials.flatten.min_by(&:priority)
  end

  def first_data_credential_for_order(order)
    credentials = []
    order.items.each do |item|
      coll = first_data_credentials.select { |each| each.categories_ids.present? && (each.categories_ids & item.variant.categories.collect(&:id)).present? }
      if coll.blank?
        credentials << first_data_credentials.select {|each| each.categories_ids.blank?}
      else
        credentials << coll
      end
    end
    credentials.flatten.min_by(&:priority)
  end

  def installments_for(cart, bin_number, doc_type, doc_number, store_bines)
    installments_for = []
    if store_bines
      bin = bines.unexpired.find_by(number: bin_number)
      if bin.present?
        installments_for = bin.available_installments_for_checkout(cart, doc_type, doc_number)
      end
    else
      installments_for = installments
      # if !(["cabal_debito", "visa_debito"].include? brand)
      unless cart.items.map(&:shop).any? { |each| each.payment_program_for(self).nil? }
        payment_plans = (cart.items.map(&:shop).map { |each| each.payment_program_for(self) }).compact.uniq
        installments_for = payment_plans.map(&:installments)
      end
      # end
      installments_for = installments_for.flatten
    end

    installments_for.sort_by(&:number)
  end

  def strategy_gateway_installments
    @strategy_gateway_installments ||= Strategies::Gateways::Installments.strategy(self)
  end

  def strategy_insurances(cart = nil)
    @strategy_insurances ||= Strategies::Insurances::strategy(self, cart)
  end

  def strategy_renaper
    @strategy_renaper ||= Strategies::RenaperStrategy::strategy(self)
  end

  def strategy_finder
    @strategy_finder ||= Strategies::Finder::strategy(self)
  end

  def first_purchase_enabled?
    #Armar strategy o tomar de whitelist_configurations
    id == 47 || id == 48
  end

  def require_login_at_checkout?
    require_login?
  end

  def require_login?
    # Agrego este metodo para no hardcodear en otros archivos mas, pero faltaria que lo tome de la config de la store
    id == 41
  end

  private

  def generate_token
    self.token = loop do
      random_token = SecureRandom.urlsafe_base64(nil, false)
      break random_token unless Mkp::Store.exists?(token: random_token)
    end
  end
end
