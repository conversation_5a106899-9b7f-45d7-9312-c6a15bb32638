class CreateApiProductsWorker
  include ApiProductActions

  def execute(current_user, product_params)
    product_attr_builder = Api::V1::AttributesBuilder.new(current_user, product_params)
    product_attr_builder.perform

    return [:error, { message: product_attr_builder.error }.to_json] if product_attr_builder.error.present?

    whodunnit = "#{current_user.id} - #{current_user.email}"

    AuditHelper.with_whodunnit(whodunnit, 'create_api_products_worker') do
      @entity = ::Mkp::Product.create(product_attr_builder.result)

      return [:error, { message: @entity.errors.messages.to_s }.to_json] if @entity.errors.present?

      post_create_actions(product_attr_builder)

      [:ok, { message: 'success' }.to_json]
    end
  end
end
