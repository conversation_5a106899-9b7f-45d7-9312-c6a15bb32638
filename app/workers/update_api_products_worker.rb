class UpdateApiProductsWorker
  include ApiProductActions

  def execute(current_user_id, product_id, product_params)
    begin
      @entity = Mkp::Product.find(product_id)
      current_user = User.find(current_user_id)

      whodunnit = "#{current_user.id} - #{current_user.email}"

      AuditHelper.with_whodunnit(whodunnit, 'update_api_products_worker') do
        product_attr_builder = Api::V1::AttributesBuilder.new(current_user, product_params.merge(shop_id: @entity.shop_id), @entity)
        product_attr_builder.perform

        return [:error, { message: product_attr_builder.error }.to_json] if product_attr_builder.error.present?

        initialize_variants_to_delete(product_attr_builder)
        initialize_package_to_delete(product_attr_builder)
        @entity.update(product_attr_builder.result)

        return [:error, { message: @entity.errors.messages.to_s }.to_json] if @entity.errors.present?

        post_update_actions(product_attr_builder)

        [:ok, { message: 'success' }.to_json]
      end
    rescue Exception => e
      return [:error, {message: e.message}.to_json]
    end
  end
end
