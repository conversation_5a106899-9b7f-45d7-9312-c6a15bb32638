# frozen_string_literal: true

module Cancelation
  # Base behavior to cancel orders or suborder.
  class CancelationService
    attr_reader :order, :suborder, :motive, :send_notification, :valid, :coupon,
                :error, :can_credit_note

    CANCELLATION_MAILERS = %i[
      customer_purchase_cancellation_notification
      network_admin_purchase_cancellation_notification
    ].freeze

    EXCHANGE_MAILERS = %i[
      customer_purchase_change_notification
      network_admin_purchase_change_notification
    ].freeze

    REFUND_MAILERS = %i[
      customer_purchase_refund_notification
      network_admin_purchase_refund_notification
    ].freeze

    def initialize(order:, suborder: nil, items:, motive: nil, action: :cancel,
                   send_notification: true, can_credit_note: true, manual: false)
      @order = order
      @suborder = suborder
      @motive = motive
      @action = action
      @send_notification = send_notification
      @can_credit_note = can_credit_note
      @items = Mkp::OrderItem.where(id: items)
      @valid = false
      @manual = manual
    end

    def cancel
      if validate_order_permission
        cancel_payments! if @order.store.cancel_payments
        if validate_payments
          cancel_shipments! if cancel?
          generate_refund_shipments if generate_refund_shipments?
          childs_cancelation!
          update!
          generate_coupon! if cupon? && @order.store.generate_coupons
          if cancel?
            notify_cancelation!
            cancel_policy if cancel_warranty?
          else
            notify_refund!
          end
          credit_note_generation! if @order.store.generate_credit_notes
          run_update_supplier_stock(@order)
          @valid = true
        else
          @error = 'Error cancelando el pago'
        end
      end
      @valid
    rescue StandardError => e
      # Stores::ErrorMailer.throw_error_in_store(e.to_s,
      #                                        e.backtrace,
      #                                        order.store).deliver_later
      # false
      raise
    end

    def exchange
      generate_exchange_shipments
      update!
      notify_exchange!
      @valid = true

      @valid
    rescue StandardError => e
      Stores::ErrorMailer.throw_error_in_store(e.to_s,
                                               e.backtrace,
                                               order.store).deliver_later
      false
    end

    private

    def object_for_cancel
      raise 'Subclasses must implement this method'
    end

    def objects_to_update
      raise 'Subclasses must implement this method'
    end

    def credit_note_generation!
      raise 'Subclasses must implement this method'
    end

    def cancel_payments!
      raise 'Subclasses must implement this method'
    end

    def validate_payments
      raise 'Subclasses must implement this method'
    end

    def validate_order_permission
      cancel? ? @order.is_cancellable? : @order.is_refundable?
    end

    def cancel_shipments!
      object_for_cancel.shipments.each do |s|
        status_manage_class.status_change(s, 'cancelled')
      end
    end

    def childs_cancelation!
      object_for_cancel.items.each do |item|
        if item.has_external_objects?
          IntegrationsService.remote_sync(item.product, item.variant)
        end
      end
    end

    def cupon?
      motive.present? ? motive.dig(:type) == 'Cupon' : false
    end

    def notify_cancelation!
      layout = Mkp::Store::BNA_STORE_IDS.include?(@order.store.id) ? 'v5/mailer/bna/purchase_cancellation' : nil

      return unless send_notification

      return Mkp::OrderMailer.delay_for(35.seconds).customer_purchase_cancellation_notification(object_for_cancel, 'AR', @coupon, layout) if @order.store_id == 41

      CANCELLATION_MAILERS.each do |mailer|
        if mailer == :customer_purchase_cancellation_notification
          order_mailer.send(mailer, object_for_cancel, 'AR', @coupon, layout)
        else
          order_mailer.send(mailer, object_for_cancel, 'AR', @coupon)
        end
      end
    end

    def notify_refund!
      return unless send_notification

      REFUND_MAILERS.each do |mailer|
        order_mailer.send(mailer, order, 'AR')
      end
    end

    def notify_exchange!
      return unless send_notification

      EXCHANGE_MAILERS.each do |mailer|
        order_mailer.send(mailer, order, 'AR')
      end
    end

    def update!
      AuditHelper.with_whodunnit('system-job:cancelation_service', 'update!') do
        objects_to_update.each do |suborder|
          if cancel?
            data = suborder.data.merge!({ 'cancel': motive })
            suborder.update(data: data)
          elsif exchange?
            data = suborder.data.merge!({ 'exchange': Time.zone.now.to_s })
            suborder.update(data: data)
          else
            suborder.update_attribute(:refunded, true)
          end
        end
      end
    end

    def cancel?
      @action == :cancel
    end

    def exchange?
      @action == :exchange
    end

    def refund?
      @action == :refund
    end

    def generate_refund_shipments
      @items.group_by(&:suborder).each do |suborder, items|
        actual_shipment = suborder.shipment
        manager = Mkp::ShipmentsManager.new(@order, suborder,
                                            items, nil,
                                            actual_shipment.destination_address,
                                            actual_shipment)
        manager.create_shipment('refund')
      end
    end

    def generate_exchange_shipments
      Rails.logger.info("cavernicola")
      Rails.logger.info("---------------------------------------------------------")
      Rails.logger.info("Inicio de creado de envios por cambio")
      Rails.logger.info("Items: #{@items.map(&:id).join("-")}")
      @items.group_by(&:suborder).each do |suborder, items|
        Rails.logger.info("Procesando suborder: #{suborder.id}, items: #{items.map(&:id).join("-")}")
        actual_shipment = suborder.shipment
        manager = Mkp::ShipmentsManager.new(suborder.order, suborder,
                                            items, nil,
                                            actual_shipment.destination_address,
                                            actual_shipment)
        if suborder.shipments.where(shipment_kind: ['exchange_change', 'exchange_refund']).to_a.all? {|each| each.delivered? || each.not_delivered?}
          manager.create_shipment('exchange_change')
          sleep 1
          manager.create_shipment('exchange_refund')
        else
          raise "Existen envios sin entregar. No es posible generar un nuevo envio para cambio"
        end
      end
      Rails.logger.info("Finaliza creado de envios por cambio")
      Rails.logger.info("---------------------------------------------------------")
    end

    def run_update_supplier_stock(order)
      SupplierStock.create_return_from(order)
    end

    def generate_coupon!
      @coupon = Mkp::Coupon::Network.create(
        type: 'Mkp::Coupon::Network',
        code: "#{@order.id}#{Mkp::Coupon::Network.random_coupon}",
        description: coupon_description,
        store_id: @order.store_id,
        policy: 'value',
        minimum_value: 0,
        amount: object_for_cancel.total,
        total_available: 1,
        starts_at: Time.zone.now,
        expires_at: Time.zone.now + 90.days,
        apply_on_sale: 1,
        network: 'AR'
      )
    end

    def generate_refund_shipments?
      !cancel? && @order.store.generate_refund_shipments
    end

    def status_manage_class
      Mkp::StatusChange::EntityStatusManage
    end

    def order_mailer
      Mkp::OrderMailer.delay_for(35.seconds)
    end

    def coupon_description
      "Cupon por cancelación de #{cancelable_class}"\
      " #{object_for_cancel.id} - Motivo: #{@motive}"
    end

    def cancelable_class
      object_for_cancel.class.name.split('::').last.downcase
    end

    def cancel_warranty?
      @order.shops.any? { |shop| shop.id == OCHENTA_SHOP_ID }
    end

    def cancel_policy
      suborder_warranty = @order.suborders.select(&:warranty?).first
      policy_to_cancel = WarrantyResponse.where(suborder_id: suborder_warranty.id).first
      # Avenida::Blister::PolicyRequest.new(order: @order).cancel_policy(policy_to_cancel)
      Avenida::Boston::PolicyCancel.call(policy_to_cancel: policy_to_cancel)
    end
  end
end
