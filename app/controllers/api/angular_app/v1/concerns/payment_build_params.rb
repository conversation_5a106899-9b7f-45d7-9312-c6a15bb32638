module Api::AngularApp::V1::Concerns::PaymentBuildParams
  extend ActiveSupport::Concern

  def set_payment_params
    # move this code to another method
    return nil unless self.respond_to?(ERB::Util.html_escape(params[:gateway]))
    gateway_params = send(ERB::Util.html_escape(params[:gateway]))
    raise "El medio de pago elegido no es del tipo correcto" if gateway_params[:gateway_class].try(:qr_type?)
    if @checkout_cart.own_gateways?(gateway_params[:document_number] || gateway_params[:doc_number])
      Rails.logger.info("OWN GATEWAYS INSTALLMENT #{params[:gateway]} =>  #{gateway_params[:document_number] || gateway_params[:doc_number]}")
      return nil if !@checkout_cart.own_gateways.include? params[:gateway]
    end
    parameter_filter = ActionDispatch::Http::ParameterFilter.new(Rails.application.config.filter_parameters)
    Rails.logger.info("PARAMS : #{parameter_filter.filter(params)}}------------------------")
    Rails.logger.info("GWPARAMS : #{parameter_filter.filter(gateway_params)}--------------------")
    adjust_installments_by_payment_program(gateway_params)
    gateway_params
  end

  def firstdata
    Rails.logger.info("FDPARAMS:#{params}---------------------------")
    Rails.logger.info("FDPARAMS_RESPONSE: #{params[:response].inspect}")
    bin = params[:card_number].slice(0..5)
    Rails.logger.info("binPARAMS: #{bin}---------------------------")
    {
      gateway: params[:gateway],
      gateway_class: Avenida::Payments::FirstData,
      card_number: params[:card_number],
      card_expiration_month: params[:card_expiration_month],
      card_expiration_year: params[:card_expiration_year],
      security_code: params[:security_code],
      document_type: (params[:card_holder_identification] ? params[:card_holder_identification][:type] : ''),
      document_number: (params[:card_holder_identification] ? params[:card_holder_identification][:number] : ''),
      installment_number: params[:installments],
      installment_coef: params[:coef],
      installment_gov_program: installment_government_program(bin, params[:installments])
    }
  end

  def firstdata_distributed
    bin = params[:card_number].slice(0..5)
    {
      gateway: 'firstdata_distributed',
      gateway_class: Avenida::Payments::FirstDataDistributed,
      card_number: params[:card_number],
      card_expiration_month: params[:card_expiration_month],
      card_expiration_year: params[:card_expiration_year],
      security_code: params[:security_code],
      document_type: (params[:card_holder_identification] ? params[:card_holder_identification][:type] : ''),
      document_number: (params[:card_holder_identification] ? params[:card_holder_identification][:number] : ''),
      installment_number: params[:installments],
      installment_coef: params[:coef],
      installment_gov_program: installment_government_program(bin, params[:installments]),
      cardholder_name: params[:card_holder_name]
    }
  end

  def decidir
    bin = params[:response][:bin]
    installment_number = params[:installments].is_a?(Hash) ? params[:installments][:installments_number] : params[:installments]
    {
      gateway: params[:gateway],
      gateway_class: Avenida::Payments::Decidir,
      token: params[:response][:id],
      transaction_id: 123,
      installments: installment_number,
      coef: params[:coef],
      payment_method_id: decidir_payment_method_id(params[:response][:bin], params[:card_brand]),
      issuer_id: "1",
      bin: bin,
      document_type: params[:doc_type],
      document_number: params[:doc_number],
      installment_gov_program: installment_government_program(bin, installment_number),
      cardholder_name: params[:response][:cardholder][:name],
      card_last_four_digits: params[:response][:last_four_digits]
    }
  end

  def decidirdistributed
    installment_number = params[:installments].is_a?(Hash) ? params[:installments][:installments_number] : params[:installments]
    if @cart.store.id == 21
      bin = params[:card_number].dup.slice!(0..5)
      {
        gateway: 'decidir_distributed',
        gateway_class: Avenida::Payments::DecidirDistributedMacro,
        card_number: params[:card_number],
        card_expiration_month: params[:card_expiration_month],
        card_expiration_year: params[:card_expiration_year],
        security_code: params[:security_code],
        card_holder_name: params[:card_holder_name],
        transaction_id: 123,
        installments: installment_number,
        coef: params[:coef],
        payment_method_id: decidir_payment_method_id(bin, params[:card_brand]),
        issuer_id: "1",
        bin: bin,
        document_type: params[:doc_type],
        document_number: params[:doc_number]
      }
    elsif @cart.store.id != 41
      {
        gateway: 'decidir_distributed',
        gateway_class: Avenida::Payments::DecidirDistributed,
        token: params[:response][:id],
        transaction_id: 123,
        installments: installment_number,
        coef: params[:coef],
        payment_method_id: decidir_payment_method_id(params[:response][:bin], params[:card_brand]),
        installment_gov_program: installment_government_program(params[:response][:bin], installment_number),
        issuer_id: "1",
        bin: params[:response][:bin],
        document_type: params[:doc_type],
        document_number: params[:doc_number],
        cardholder_name: params[:response][:cardholder][:name],
        card_last_four_digits: params[:response][:last_four_digits]
      }.merge(tokenized_data: tokenized_decidir_attributes(params[:response]))
    else
      avenida_params_with_cards(method(:tokenized_decidir_attributes)).merge(gateway: 'decidir_distributed', gateway_class: Avenida::Payments::DecidirDistributed)
    end
  end

  def avenidadecidirdistributed
    avenida_params_with_cards(method(:tokenized_decidir_attributes)).merge(
                                                  gateway: 'decidir_distributed',
                                                  gateway_class: Avenida::Payments::AvenidaDecidirDistributed)
  end

  #token_card_method tiene que recibir card
  def avenida_params_with_cards(token_card_method)
    raise StandardError, "Debe utilizar 1 o 2 tarjetas para el pago" if (params[:cards].blank? || params[:cards].count > MAX_PAYMENT_CARDS)
    cards = params[:cards].map do |card|
      installment_number = card[:installments].is_a?(Hash) ? card[:installments][:installments_number] : card[:installments]
      card_params(card, installment_number).merge(tokenized_data: token_card_method.call(card))
    end
    {
      gateway: params[:gateway],
      issuer_id: "1",
      document_type: params[:doc_type],
      document_number: params[:doc_number],
      cards: cards
    }
  end

  def todopago
    {
      gateway: params[:gateway],
      gateway_class: Avenida::Payments::Todopago,
      public_request_key: @cart.data[:todopago]["public_request_key"],
      request_key: @cart.data[:todopago]["request_key"],
      operation_id: params[:operation_id],
      doc_number: params[:doc_number],
      doc_type: params[:doc_type]
    }
  end

  def mercadopago_ticket
    {
      gateway: "mercadopago",
      gateway_class: Avenida::Payments::Mercadopago,
      operation_id: params[:operation_id],
      doc_number: params[:doc_number],
      doc_type: params[:doc_type],
      payment_method_id: params[:payment_method_id]
    }
  end

  def mercadopago_legacy
    {
      gateway: "mercadopago",
      gateway_class: Avenida::Payments::Mercadopago,
      document_number: params[:document_number],
      document_type: params[:document_type],
      payment_method_id: params[:card_brand],
      issuer_id: params[:issuer_id],
      token: params[:token],
      installments: params[:installments],
    }
  end

  def mercadopago
    avenidamercadopago
  end

  def avenidamercadopago
    avenida_params_with_cards(method(:tokenized_mercadopago_attributes)).merge(gateway: "mercadopago", gateway_class: Avenida::Payments::AvenidaMercadopago)
  end

  def avenidamodo
    {
      gateway: "modo",
      gateway_class: Avenida::Payments::AvenidaModo,
      document_type: params[:doc_type],
      document_number: params[:doc_number],
      installments: params[:installments],
      cards: [],
      callbacks: { error: '', success: '' },
      external_program_id: @checkout_cart.external_program_id
    }
  end
  alias_method :modo, :avenidamodo

  def avenidamododistributed
    avenidamodo.merge(
      gateway: "modo_distributed",
      gateway_class: Avenida::Payments::AvenidaModoDistributed,
      cards: [])
  end

  alias_method :mododistributed, :avenidamododistributed

  def jubilo
    {
      gateway: "empty",
      gateway_class: Avenida::Payments::Empty,
      cuil: params[:cuil],
      external_id: params[:external_id],
      cuotas: params[:cuotas],
      estado: params[:estado],
      monto_cuota: params[:monto_cuota],
      monto_total: params[:external_id],
      monto_liquidado: params[:monto_liquidado],
      cft: params[:cft],
      tea: params[:tea],
      credit: params[:credit],
      response: params[:response],
      payment_status: "pending",
      key_payment_option: params[:key_payment_option],
      positive_identification_token: params[:positive_identification_token],
      seller_id: params[:seller_id]
    }
  end

  def sportclub
    {
      gateway: "empty",
      gateway_class: Avenida::Payments::Empty,
      cuil: params[:cuil],
      external_id: params[:external_id],
      cuotas: params[:cuotas],
      estado: params[:estado],
      monto_cuota: params[:monto_cuota],
      monto_total: params[:external_id],
      payment_status: "collected"
    }
  end

  def tarjeta_digital
    {
      gateway: "tarjetadigital",
      gateway_class: Avenida::Payments::Tarjetadigital,
      installments: 24,
      payment_status: "pending"
    }
  end

  def system_points
    {
      gateway: "system-points",
      gateway_class: Avenida::Payments::SystemPoint,
      cuotas: params[:cuotas],
      estado: params[:estado],
      monto_cuota: params[:monto_cuota],
      payment_status: "collected"
    }
  end

  def visa_puntos_params
    {
      document_number: params[:document_number] || params[:doc_number],
      document_type: params[:document_type],
      domicilio_completo: params[:domicilio_completo],
      codigo_postal: params[:codigo_postal],
      cod_provincia: params[:cod_provincia],
      localidad: params[:localidad],
      telefono: params[:telefono],
      cod_premio: VISAPUNTOS_COD_PREMIO
    }
  end

  def loan
    {
      gateway: "loan_bna",
      payment_method: 'bna_loan',
      gateway_class: Avenida::Payments::LoanBna,
      cards: [],
      amount: @checkout_cart.total,
      offer_code: params['offer_code'],
      loan_account_number: params['loan_account_number'],
      loan_alternative_code: params['loan_alternative_code'],
      idPPM: params['idPPM'],
      loanInstallments: params['loanInstallments']
    }
  end

  private

  def installment_government_program(bin_number, installment_number)
    if @current_store.use_bines?
      installment_government_program_by_bin?(bin_number, installment_number, @cart)
    else
      installment_government_program_by_cart?(@cart, installment_number, false)
    end
  end

  def installment_government_program_by_bin?(bin_number, installment_number, cart)
    bin = (Bin.unexpired.where number: bin_number, store: @current_store).first
    shop_program = installment_government_program_by_cart?(cart, installment_number, false)
    return true if bin.present? && shop_program
    return false
  end

  def installment_government_program_by_cart?(cart, installment_number, store_bines)
    installments = @current_store.installments_for(cart, nil, nil, nil, store_bines)
    installment = installments.find { |inst| inst.number.to_s == installment_number.to_s }
    return installment.government_program if installment
    return false
  end

  def adjust_installments_by_payment_program(gateway_params)
    # TODO delegar esto en el payment_program.
    unless @cart.items.map(&:shop).any? { |shop| shop.payment_program_for(@cart.store).nil? }
      adjust_installments_by_payment_program_for(:installment_number, gateway_params)
      adjust_installments_by_payment_program_for(:installments, gateway_params)
      adjust_installments_by_payment_program_for(:cuotas, gateway_params)
      two_cards_payment_installments(gateway_params)
    end
  end

  def two_cards_payment_installments(gateway_params)
    if @cart.store.id == 41
      gateway_params[:cards].each do |card|
        adjust_installments_by_payment_program_for(:installments, card)
      end
    end
  end

  def adjust_installments_by_payment_program_for(key, gateway_params)
    government_program = gateway_params[:installment_gov_program]
    case gateway_params[key]
    when '3'
      gateway_params[key] = government_program ? "13" : "3"
    when '6'
      gateway_params[key] = government_program ? "16" : "6"
    when '12'
      gateway_params[key] = government_program ? "7" : "12"
    when '18'
      gateway_params[key] = government_program ? "8" : "18"
    when '24'
      gateway_params[key] = government_program ? "25" : "24"
    when '30'
      gateway_params[key] = government_program ? "31" : "30"
    else
      nil
    end
  end

  def decidir_payment_method_id(bin_number, card_brand)
    # visa_recargable no está en Decidir
    payment_methods = {
      "visa" => 1,
      "amex" => 65,
      "mastercard" => 104,
      "mastercard_debito" => 105,
      "naranja" => 24,
      "visa_debito" => 31,
      "maestro" => 106,
      "cabal" => 63,
      "cabal_debito" => 108,
      "credimas" => 38,
      "diners" => 8,
      "nativa" => 42
    }
    bin = (Bin.unexpired.where number: bin_number, store: @current_store).first
    return payment_methods[bin.brand] if bin
    payment_methods[card_brand]
  end

  # Todavia no se usa
  def avenida_payment_method_id(bin_number, card_brand)
    bin = (Bin.unexpired.where number: bin_number, store: @current_store).first
    return bin.brand if bin
    card_brand
  end


  def card_params(card, installment_number)
    {
      token: card[:id],
      transaction_id: 123,
      installments: installment_number,
      coef: card[:coef],
      payment_method_id: decidir_payment_method_id(card[:bin], card[:card_brand]),
      # payment_method: avenida_payment_method_id(card[:bin], card[:card_brand]),
      installment_gov_program: installment_government_program(card[:bin], installment_number),
      cardholder_name: card[:cardholder][:name],
      card_last_four_digits: card[:last_four_digits],
      cardholder_doc_number: card.dig(:cardholder, :identification, :number),
      cardholder_doc_type: card.dig(:cardholder, :identification, :type),
      bin: card[:bin],
      amount: card[:amount]
    }
  end

  def tokenized_decidir_attributes(card)
    card.slice(:id, :status, :card_number_length, :date_created, :bin, :last_four_digits, :security_code_length, :expiration_month, :expiration_year, :date_due, :cardholder)
  end

  def tokenized_mercadopago_attributes(card)
    card.slice(:id)
    # :public_key, :first_six_digits, :expiration_month, :expiration_year, :last_four_digits, :cardholder, :status, :date_created, :date_last_updated, :date_due, :luhn_validation, :live_mode, :require_esc, :card_number_length, :security_code_length)
  end
end
