module Api
  module V1
    class ApiController < ActionController::Base
      before_action :authenticate_request
      include PaperTrailContext
      protect_from_forgery with: :null_session

      attr_reader :current_user

      private

      def authenticate_request
        command = AuthorizeApiRequest.new(request.headers)
        @current_user = command.call
        render json: { error: 'Not Authorized' }, status: 401 unless @current_user
      end
    end
  end
end