require 'activerecord-import/base'
ActiveRecord::Import.require_adapter('mysql2')

class ImportCategoriesWorker
  include Sidekiq::Worker
  include Csv::Helpers
  sidekiq_options queue: `hostname`.strip, retry: false

  def perform(file_path)

    unless File.exist?(file_path)
      Rails.logger.info "No se encontro el archivo #{file_path}"
      return
    end

    delimiter = find_delimiter(file_path) || DEFAULT_DELIMITER

    AuditHelper.with_whodunnit('system-job:import_categories_worker', 'perform') do
      CSV.foreach(file_path, headers: true, col_sep: delimiter,
                             encoding: 'utf-8', header_converters: :symbol) do |row|

        parent_name = row.to_hash.slice(:parent)[:parent]
        params = row.to_hash.slice!(:parent)

        attributes = filter_params(params)

        category = new_category(parent_name, attributes)
        next unless category.save!
      end
    end
  end

  private

  def filter_params(params)
    attributes = set_booleans(params.slice(:name, :slug, :active)).merge(network: 'AR')

    if attributes.size != 4
      Rails.logger.info 'Faltan Parámetros'
      return
    end

    attributes
  end

  def new_category(parent_name = nil, attributes = {})
    if parent_name.nil?
      Mkp::Category.new(attributes)
    else
      parent = Mkp::Category.find_by(name: parent_name)
      Mkp::Category.new(attributes) do |c|
        c.parent = parent
        c.full_path_name = "#{parent.full_path_name}/#{c.name}"
      end
    end
  end

  def set_booleans(params)
    case params[:active].downcase
    when 'true'
      params[:active] = true
      params
    when 'false'
      params[:active] = false
      params
    else
      Rails.logger.info 'Valor invalido para el campo active'
    end
  end
end