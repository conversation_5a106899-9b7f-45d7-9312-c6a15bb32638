module Pioneer
  class ApplicationController < ActionController::Base
    include Pioneer::Concerns::RenderProperly # Cavi -> We should explicity declare like this, because some weird situation
    include PaperTrailContext

    rescue_from CanCan::AccessDenied do |exception|
      redirect_to home_url, :alert => exception.message
    end

    force_ssl if SSL_ENABLED

    protect_from_forgery with: :exception

    before_filter :store_return_to_location,
                  :require_admin,
                  :set_network,
                  :set_locale

    around_filter :set_time_zone

    helper_method :current_admin_session,
                  :current_admin,
                  :current_network,
                  :current_user,
                  :current_full_network

    def home
      render template: "/pioneer/home/<USER>", layout: 'bootstrap_layout'
    end

    private

    def current_ability
      @current_ability ||= Ability::Factory.build(current_user)
    end

    def current_full_network
      current_network.thin? ? Network[Network.default] : current_network
    end


    def require_admin
      redirect_to(new_session_path) if current_admin.nil?
    end

    def require_no_admin
      redirect_to(home_url) if current_admin.present?
    end

    def set_network
      @network = current_admin.network
    end

    def set_locale
      I18n.locale = current_network.locale
    end

    def set_time_zone(&block)
      return block.call unless current_network.time_zone.present?
      Time.use_zone(current_network.time_zone, &block)
    end

    def current_admin_session
      # This sentences is because the weird
      # of Authlogic::Session::Activation::NotActivatedError
      ::Authlogic::Session::Base.controller = ::Authlogic::ControllerAdapters::RailsAdapter.new(self)
      @current_admin_session ||= Session.find
    end

    def current_admin
      @current_admin ||= current_admin_session && current_admin_session.admin
    end

    def current_user
      @current_user ||= current_admin
    end

    def current_network
      @current_network ||= Network[@network]
    end

    def store_return_to_location
      if !current_admin.present? && request.get? && !request.xhr?
        session[:return_to] = request.url
      end
    end

    def user_for_paper_trail
      current_admin.present? ? current_admin.id : 'Public user Pioneer'
    end
  end
end
