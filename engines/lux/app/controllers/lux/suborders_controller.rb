# frozen_string_literal: true

module Lux
  class SubordersController < Lux::ApplicationController
    include Reporting::Streamable
    before_filter :load_suborder, only: :show
    before_filter :find_associated_data, only: :show


    BNA_MIMOTO_ID = 43.freeze

    helper_method :packing_list_url,
                  :shipment_label_url,
                  :shipment_label_forms_url,
                  :status_selector,
                  :order_item_selector
                  :status_product

    def status_selector
      Mkp::Shipment.options_for_select.map { |each| [t("pioneer.orders.#{each[1]}"), each[1]] }
    end

    def order_item_selector
      if @shop.stores.map(&:id) == [BNA_MIMOTO_ID]
        Mkp::OrderItem.bna_options_for_select.map do |each|
          [t("pioneer.orders.#{each[1]}"), each[1]]
        end
      else
        Mkp::OrderItem.options_for_select.map { |each| [t("pioneer.orders.#{each[1]}"), each[1]] }
      end
    end

    def suborder_label_import_create
      @suborder_label_import_form = SuborderLabelImportForm.new(params[:suborder_label_import_create])
      if @suborder_label_import_form.save
        @suborder_label_upload_status = "Su CSV ha sido cargado satisfactoriamente, revise su correo #{@shop.owner.email} para verificar errores"
        suborder_label_list_path = @suborder_label_import_form.suborder_label_list.path
        worker_id = Mkp::ImportLabelsSubordersWorker.perform_async(suborder_label_list_path, @shop.id)
        redirect_to label_processing_shop_suborders_path(id: worker_id, strategy: params[:suborder_label_import_create][:strategy], csv_status:@suborder_label_upload_status )
      else
        @suborder_label_upload_status = "Hubo un error, intente procesar el CSV nuevamente"
        redirect_to label_processing_shop_suborders_path(strategy: params[:suborder_label_import_create][:strategy], csv_status:@suborder_label_upload_status )
      end
    end

    def suborder_label_processing
      @suborder_label_upload_status = params[:csv_status]
    end

    def new
      @suborder_label_import_form = SuborderLabelImportForm.new({strategy: params[:strategy]})
    end

    def index
      console if Rails.env.development?
      set_filters
      start_date = @start_date
      end_date = @end_date
      shop = @shop
      order = @order
      filter_by = @filter_by
      query = @query
      shipment_status = @shipment_status
      shipment_type = @shipment_type
      order_item_status = @order_item_status
      external_redemption_completed = @external_redemption_completed
      search = Mkp::Suborder.search(include: suborder_includes) do
        all_of do
          with(:shop_id, shop.id)
          with(:shipment_status, shipment_status) if shipment_status.present?
          with(:logistic_type, shipment_type) if shipment_type.present?
          without(:payment_status, ['pending', 'expired'])
        end
        with(:order_item_status, order_item_status) if order_item_status.present?
        with(:office_id, current_user.office.id) if current_user.office.present?
        if params[:start_date].present? && params[:end_date].present?
          with(filter_by.first, start_date..end_date)
        end
        if external_redemption_completed.present? &&
            external_redemption_completed.eql?('1')
          with(:external_redemption_completed, false)
        end
        if query.present?
          if query =~ URI::MailTo::EMAIL_REGEXP
            with(:customer_email, query)
          else
            fulltext query
          end
        end
        order_by(*order, :desc)
        paginate page: (params[:page] || 1), per_page: per_page
      end
      @suborders = search.results

      respond_to do |format|
        format.html
        format.csv { render_csv(SuborderExporter, suborders: @suborders, shop: @shop) }
      end
    end

    def show
      @documentation = Mkp::Document.where(suborder_id: @suborder.id).last
      @customer_reservation = @suborder&.order&.customer_reservation_purchases
      @select_document_types = Mkp::Document.document_types.map { |type| [type, type]}
      respond_to do |format|
        format.html
        format.json { render json: render_suborder_json(@suborder) }
      end
    end

    def apply_state_change
      @suborder = @shop.suborders.includes(items: %i[product variant]).find(params[:suborder_id])

      Rails.logger.info(
          "Trying to apply state change from #{@suborder.status} to #{params[:next_state_action]}"\
        " for suborder ID: #{@suborder.id}"
      )

      @suborder.apply_state_change(params[:next_state_action])
      redirect_to :back
    end

    def upload_document
      respond_to do |format|
        if document_params.nil?
          format.html { redirect_to :back, notice: 'Error: No existen parámetros' }
          format.json { render json: { error: 'No existen parámetros' }, status: :unprocessable_entity }
        elsif document_params[:pdf_file].nil?
          format.html { redirect_to :back, notice: 'Error: No existe documento PDF cargado' }
          format.json { render json: { error: 'No existe documento PDF cargado' }, status: :unprocessable_entity }
        else
          @document = Mkp::Document.new(document_params)
          if @document.save
            format.html { redirect_to :back, notice: 'La subida de los archivos fue exitosa.' }
            format.json { render json: @document, status: :ok }
          else
            format.html { redirect_to :back, notice: 'Error: Subida de archivos limitada 2MB y Archivos PDF' }
            format.json { render json: @document.errors, status: :unprocessable_entity }
          end
        end
      end
    end

    def suborder_pick_up_update_file; end

    def suborder_pick_up_updater
      file_path = params["suborder_pick_up_update_shop_suborders"]["file"].path
      Mkp::UpdateOrdersStatusByPickup.perform_async({ file_path: file_path, shop_ids: current_user.shop_ids })
      flash[:success] = 'Su CSV está siendo procesado'
      redirect_to shop_suborders_path
    end

    def change_to_ready_to_pickup
      suborder = Mkp::Suborder.find(params['mkp_suborder']['suborder_id'])
      shipment = suborder.shipment
      shipment_items = shipment.items

      Mkp::UpdateOrderShipmentStatus.perform_async(shipment.id, '2') if suborder.present?
      Mkp::StatusChange::OrderItemStatusManage.status_change(shipment_items, 'ready_to_pick_up')

      shipment.notify_pick_up_to_customer

      redirect_to shop_suborders_path, notice: 'Orden lista para retirar'
    end

    def change_pick_up_to_delivered
      suborder = Mkp::Suborder.find(params['mkp_suborder']['suborder_id'])
      shipment = suborder.shipment
      shipment_items = shipment.items

      Mkp::UpdateOrderShipmentStatus.perform_async(shipment.id, '1') if suborder.present?
      Mkp::StatusChange::OrderItemStatusManage.status_change(shipment_items, 'delivered')

      shipment.notify_pick_up_delivered_to_customer

      redirect_to shop_suborders_path, notice: 'Orden entregada'
    end

    private

    def load_suborder
      return if @suborder.present?

      public_id_parts = params[:id].split('-')
      if public_id_parts.length == 3
        @suborder = @shop.suborders.includes(items: %i[product variant])
                        .find(public_id_parts[1])
      else
        redirect_to(shop_suborders_path) && nil
      end
    end

    def start_date(date)
      (date.present? && Time.zone.parse(date) || (Time.zone.now - 30.days))
          .to_datetime.beginning_of_day
    end

    def end_date(date)
      ((date.present? && Time.zone.parse(date)) || Time.zone.now)
          .to_datetime.end_of_day
    end

    def order_filter(order)
      order.present? ? Array(order).map(&:to_sym) : [:created_at]
    end

    def filtered(filter_by)
      filter_by.present? ? Array(filter_by).map(&:to_sym) : [:created_at]
    end

    def set_filters
      @start_date = start_date(params[:start_date])
      @end_date = end_date(params[:end_date])
      @query = params[:query]
      @order = order_filter(params[:order])
      @filter_by = filtered(params[:filter_by])
      @shipment_status = params[:shipment_status]
      @shipment_type = params[:shipment_type]
      @order_item_status = params[:order_item_status]
      @external_redemption_completed = params[:external_redemption_completed]
    end

    def suborder_includes
      [:coupon, :shop, :items, :office, order: %i[customer coupon payments]]
    end

    def per_page
      params[:format] == 'csv' ? 10000 : 25
    end

    def shipment_label_url(shipment = nil)
      label = shipment.label
      return if label.blank? || label.gateway.blank?

      label.url
    end

    def shipment_label_forms_url(shipment = nil)
      label = shipment.label
      label.url
    end

    def packing_list_url(shipment = nil)
      defused_shipment_packing_list_url(defused_parameters(shipment))
    end

    def defused_parameters(shipment = nil)
      { suborder_id: @suborder.id, token: communication_token(shipment) }
    end

    def communication_token(shipment = nil)
      encryptor.encrypt_and_encode((shipment.present? ? shipment.id : @suborder.shipment.id).to_s)
    end

    def encryptor
      NaiveTokenEncryptor.new(@suborder.encryptor_key, @suborder.encryptor_initialization_vector)
    end

    def render_suborder_json(suborder)
      {
          suborder_id: suborder.id,
          item_ids: suborder.items.map(&:id)
      }
    end

    def document_params
      params.require(:mkp_document).permit(:pdf_file, :pdf_file_type, :suborder_id)
    end

    def find_associated_data
      return unless @suborder.warranty?
      order = @suborder.order
      @associated_variant = @suborder.order.data['associated_variant']
      product = @associated_variant&.product
      @associated_product = product&.shop_id == @shop.id ? nil : product
      @price_associated_product = order.decorate.sale_price_by_product_associated(@associated_product, @associated_variant) if @associated_product.present? && @associated_variant.present?
    end
  end
end
