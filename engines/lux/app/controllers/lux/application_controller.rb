module Lux
  class ApplicationController < ActionController::Base
    include Erro<PERSON><PERSON>andler
    include CurrencyExposure
    include SetLocale
    include NetworkExposure
    include ReturnLocationStorer
    include UserSessionator

    include IntegrationsLoaders
    include PaperTrailContext

    protect_from_forgery with: :exception

    before_filter :set_network,
                  :set_locale,
                  :load_new_session,
                  :store_return_to_location,
                  :check_user_has_network,
                  :require_current_logged_in_user_can_manage_a_shop,
                  :set_current_shop,
                  :stay_same_area_when_change_shop

    append_before_filter :load_authorized_integrations, if: proc { Network[@network].shop_integrations.present? }

    layout 'lux/application'
    helper_method :shop_home_path
    helper :all

    # def home
    #   return redirect_to shop_home_path(@shop) if current_user.office.nil?
    #
    #   redirect_to shop_suborders_path(shop_id: @shop.id)
    # end

    def home
      redirect_to shop_home_path(@shop)
    end

    protected

    def shop_home_path(shop)
      shop_suborders_path(shop_id: shop.id)    
      #shop_products_path(shop_id: shop.id)
    end

    private

    def url_for(options = nil)
      if options.is_a?(Hash) && options.key?(:controller) && @network.present?
        if options[:network].blank? && /^lux\// =~ options[:controller]
          options[:network] = @network.downcase
        end
      end

      super options
    end

    def stay_same_area_when_change_shop
      return unless params[:stay_on].present?
      begin
        action = params[:stay_on] == 'lux/accounts' ? 'show' : 'index'
        redirect_to(controller: params[:stay_on],
                    action:  action,
                    network: @shop.network.downcase,
                    shop_id: @shop) && return
      rescue
      end
    end

    def get_out_of_here
      if logged_in?
        redirect_to('/shop')
      else
        redirect_to main_app.login_url
      end
    end

    def require_current_logged_in_user_can_manage_a_shop
      get_out_of_here unless current_logged_in_user_can_manage_a_shop?
    end

    def current_logged_in_user_can_manage_a_shop?
      logged_in? && current_logged_in_user.shops.any?
    end

    def set_current_shop
      shop = if params[:shop_id].present?
        current_user.shops.find_by(slug: params[:shop_id], network: @network) || current_user.shops.find_by(id: params[:shop_id], network: @network)
      else
        current_user.shops.where(network: @network).first
      end

      if shop.blank?
        # Usage of Lux::Shop instead Mkp::Shop
        @shop = Shop.find(current_user.shops.first.id)
        redirect_to shop_products_path(@shop, network: @shop.network.downcase)
      else
        @shop = Shop.find(shop.id) # Usage of Lux::Shop instead Mkp::Shop
      end
    end

    def check_user_has_network; end

    def user_for_paper_trail
      current_user.present? ? current_user.id : 'Public user Lux'
    end
  end
end
