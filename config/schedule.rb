APP_MASTER = ['ip-10-0-2-218'].freeze
# Gestión Valentino
# El pase de sportclub a partir de marzo esta bien
APP_SLAVES = [].freeze

set :output, '/home/<USER>/avenida_platform/log/whenever.log'

# ALL SERVERS.
every :reboot do
  runner 'MaxMindDB.update_if_needed'
end

every :day, at: '3:00 am' do
  runner 'MaxMindDB.update'
end

every :day, at: '11:59 pm' do
  rake 'gp:delete_files_bna:purge'
end

every :day, at: '6:23 am' do
  rake 'gp:sitemap:generate:all'
end

every :day, at: '4:11 am' do
  rake 'gp:pictures:purge'
end

every :day, at: '4:15 am' do
  rake 'cleanup:old_sales_products_exports'
end

# every 35.minutes do
#   rake 'gp:shipments:refresh'
# end

every 30.minutes do
  rake 'avenida:abandoned_carts:notify_emblue'
end

every 1.hour do
  rake 'gp:shipments:fulfil_pending_shipments'
end

every 1.hour do
  rake "payments:update_recent_payments"
end

every 1.day, at: '2:00 am' do
  rake "reservations:update_bna_to_overdue"
end

every 1.hour do
  rake "shipments:update_recent_shipments"
end

every :day, at: ['11:30 am', '6:30 pm'] do
  rake 'gp:orders:auto_invoice'
end

every :day, at: '6:00 am' do
  rake 'gp:orders:cancel_orders_with_expired_payments'
end

every :day, at: '6:05 am' do
  rake 'gp:orders:cancel_tarjetadigital_orders_with_expired_payments'
end

every :day, at: ['10:00 am', '1:00 pm', '5:00 pm', '8:00 pm', '11:00 pm'] do
  rake 'avenida:smart_reindex:prods_vars'
end

# every :day, at: ['8:50 am'] do
#   rake 'avenida:emblue_file_updater'
# end

# every :day, at: ['5:30 am'] do
#   rake 'avenida:full_reindex:reindex_worker'
# end

every 15.minutes do
  rake 'avenida:ochenta:fetch_extended_warranties'
end

every 2.hour do
  rake 'avenida:left_cart_notify'
end

every 3.minutes do
  puts "Cancelando pagos de Modo pendientes... every 3.minutes"
  rake 'loyalty_bna:rollback_points_modo_pending'
end

every 3.minutes do
  rake 'avenida:modo:process_expired_payments'
end

# ONLY APP_MASTER SERVER.
if `hostname`.strip == APP_MASTER.first
  every :day, at: '2:11 am' do
    rake 'gp:system_communications:generate'
  end

  every :day, at: '3:03 am' do
    rake 'gp:abandoned_carts:generate_report'
  end

  every :day, at: '4:17 am' do
    rake 'gp:homepage:update'
  end

  every :day, at: '5:07 am' do
    rake 'gp:solr:scoring:calculate'
  end

  every :day, at: '5:23 am' do
    rake 'gp:solr:scoring:build'
  end
end

# TO BE DISTRIBUTED ALONG APP_MASTER + APP_SLAVES.
APP_SERVERS = APP_MASTER + APP_SLAVES

def ar_product_feeds_generation
  every :day, at: '2:00 am' do
    rake 'gp:product:feeds:create_ar'
  end
end

def count_sku_by_network
  every :day, at: '04:00 am' do
    rake 'gp:sku:report'
  end
end

# TODO: find out why are old suborders being used here and uncomment this
# def shop_reminder_notification
#   every :day, at: '6:45 am' do
#     rake 'gp:orders:shop_reminder_notification'
#   end
# end

def clean_menu_keys
  every :day, at: '9:00 am' do
    rake 'gp:redis:menu'
  end
end

def restore_shipments
  every :day, at: '4:32 am' do
    rake 'gp:shipments:restore'
  end
end

def facebook_feed_monday
  every :monday, at: '2:00 am' do
    rake 'gp:feed:generate'
  end
end

def move_feed_to_public_monday
  every :monday, at: '4:00 am' do
    rake 'gp:feed:move_feed'
  end
end

def facebook_feed_thursday
  every :thursday, at: '2:00 am' do
    rake 'gp:feed:generate'
  end
end

def move_feed_to_public_thursday
  every :thursday, at: '4:00 am' do
    rake 'gp:feed:move_feed'
  end
end

def products_export
  every :day, at: '1:00 am' do
    rake 'gp:products:export'
  end
end

def delete_old_documents_movilidad
  every 1.day, at: '2:00 am' do
    rake 'mkp:delete_old_documents_movilidad'
  end
end

TASKS = [
  method(:restore_shipments),
  method(:facebook_feed_monday),
  method(:move_feed_to_public_monday),
  method(:facebook_feed_thursday),
  method(:move_feed_to_public_thursday),
  method(:products_export),
  method(:delete_old_documents_movilidad),
].freeze

AMOUNT_APP_SERVERS = APP_SERVERS.length

TASKS.each_with_index do |task, index|
  server_hostname = APP_SERVERS[index % AMOUNT_APP_SERVERS]
  if `hostname`.strip == server_hostname
    task.call
  end
end
