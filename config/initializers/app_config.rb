env = ENV['RAILS_ENV'] || Rails.env

AFFILIATES                  = APP_CONFIG[env]['affiliates']

AWS_ACCESS_KEY              = APP_CONFIG[env]['aws']['access_key_id']
AWS_SECRET_ACCESS_KEY       = APP_CONFIG[env]['aws']['secret_access_key']
AWS_TDD_BUCKET              = APP_CONFIG[env]['aws']['tdd']['bucket']

AWS_EMBLUE_ACCESS_KEY        = APP_CONFIG[env]['aws']['emblue']['access_key_id']
AWS_EMBLUE_SECRET_ACCESS_KEY = APP_CONFIG[env]['aws']['emblue']['secret_access_key']
AWS_EMBLUE_BUCKET_NAME       = APP_CONFIG[env]['aws']['emblue']['bucket_name']
AWS_EMBLUE_REGION            = APP_CONFIG[env]['aws']['emblue']['region']

ANGULAR_API_KEY             = APP_CONFIG[env]['angular']['api_key']

BRAINTREE_ENV               = APP_CONFIG[env]['braintree']['env'].to_sym
BRAINTREE_MERCHANT_ID       = APP_CONFIG[env]['braintree']['merchant_id']
BRAINTREE_PRIVATE_KEY       = APP_CONFIG[env]['braintree']['private_key']
BRAINTREE_PUBLIC_KEY        = APP_CONFIG[env]['braintree']['public_key']

COLPPY_CLIENT               = APP_CONFIG[env]['colppy']['client']
COLPPY_COMPANY_ID           = APP_CONFIG[env]['colppy']['company_id']
COLPPY_USER                 = APP_CONFIG[env]['colppy']['user']

COLPPY                      = APP_CONFIG[env]['colppy']

EQUIFAX_TOKEN_API_ENDPOINT_PRD        = APP_CONFIG[env]['equifax']['token']['api_token_endpoint_prd']
EQUIFAX_TOKEN_API_ENDPOINT_UAT        = APP_CONFIG[env]['equifax']['token']['api_token_endpoint_uat']
EQUIFAX_CLIENT_API_ENDPOINT_PRD       = APP_CONFIG[env]['equifax']['client']['api_client_endpoint_prd']
EQUIFAX_CLIENT_API_ENDPOINT_UAT       = APP_CONFIG[env]['equifax']['client']['api_client_endpoint_uat']

EASYPOST_API_KEY            = APP_CONFIG[env]['easypost_api_key']

ENABLE_SIDEKIQ_WEB          = APP_CONFIG[env]['enable_sidekiq_web']

EXTERNAL_COMMUNICATIONS_KEY = APP_CONFIG[env]['external_communications']['key']

FACEBOOK_API                = APP_CONFIG[env]['facebook_api']
FACEBOOK_SECRET             = APP_CONFIG[env]['facebook_secret']
FACEBOOK_PIXEL              = APP_CONFIG[env]['facebook_pixel']

GOOGLE_API                  = APP_CONFIG[env]['google']['api']
GOOGLE_ANALYTICS            = APP_CONFIG[env]['google']['analytics']
GOOGLE_ANALYTICS_REPORTER   = APP_CONFIG[env]['google']['analytics_reporter_account']
GOOGLE_CONVERSION_ID        = APP_CONFIG[env]['google']['conversion_id']
GOOGLE_TAG_MANAGER          = APP_CONFIG[env]['google']['tag_manager']
GOOGLE_MAPS                 = APP_CONFIG[env]['google']['maps']

HOSTNAME                    = APP_CONFIG[env]['hostname']

INCLUDE_ANALYTICS           = APP_CONFIG[env]['include_analytics']
INCLUDE_OLARK_CHAT          = APP_CONFIG[env]['include_olark_chat']

ISSUU_API                   = APP_CONFIG[env]['issuu_api']
ISSUU_SECRET                = APP_CONFIG[env]['issuu_secret']

LION_KEY                    = APP_CONFIG[env]['lion_key']

ATENE_KEYS                  = APP_CONFIG[env]['atene']['keys']
ATENE_URL                   = APP_CONFIG[env]['atene']['url']

BOCA_KEY                   = APP_CONFIG[env]['boca']['key']
BOCA_URL                   = APP_CONFIG[env]['boca']['url']

IRSA_CHEQUEREGALO           = APP_CONFIG[env]['irsa']['chequeregalo']

JUBILO_PRODUCTION_URL       = APP_CONFIG[env]['jubilo']['production']['url']
JUBILO_STAGING_URL          = APP_CONFIG[env]['jubilo']['staging']['url']

SYSTEM_POINT_URL            = APP_CONFIG[env]['system_point']['url']
SYSTEM_POINT_KEY            = APP_CONFIG[env]['system_point']

BRUKMAN_KEYS                = APP_CONFIG[env]['brukman']['keys']
BRUKMAN_URL                 = APP_CONFIG[env]['brukman']['url']
BRUKMAN_AUTH                = APP_CONFIG[env]['brukman']['auth_url']

MAILCHIMP                   = APP_CONFIG[env]['mailchimp']

MELI                        = APP_CONFIG[env]['mercadolibre']

MERCADOPAGO_CLIENT_ID       = APP_CONFIG[env]['mercadopago']['old']['client_id']
MERCADOPAGO_CLIENT_SECRET   = APP_CONFIG[env]['mercadopago']['old']['client_secret']
MERCADOPAGO_PUBLIC_KEY      = APP_CONFIG[env]['mercadopago']['new']['public_key']
MERCADOPAGO_ACCESS_TOKEN    = APP_CONFIG[env]['mercadopago']['new']['access_token']

FIRSTDATA_DOMAIN           = APP_CONFIG[env]['firstdata']['domain']

DECIDIR_PUBLIC_KEY          = APP_CONFIG[env]['decidir']['public_key']
DECIDIR_PRIVATE_KEY         = APP_CONFIG[env]['decidir']['private_key']
DECIDIR_ENDPOINT            = APP_CONFIG[env]['decidir']['endpoint']
AVENIDA_SITE_ID             = APP_CONFIG[env]['decidir']['avenida_site_id']
AVENIDA_JR_SITE_ID          = APP_CONFIG[env]['decidir']['avenida_jr_site_id']

DECIDIR_TDD_PRIVATE_KEY     = APP_CONFIG[env]['decidir']['tdd']['private_key']
DECIDIR_TDD_PUBLIC_KEY      = APP_CONFIG[env]['decidir']['tdd']['public_key']
DECIDIR_CIUDAD_PUBLIC_KEY   = APP_CONFIG[env]['decidir']['bancociudad']['public_key']
DECIDIR_CIUDAD_PRIVATE_KEY  = APP_CONFIG[env]['decidir']['bancociudad']['private_key']

TDIGITAL_DOMAIN            = APP_CONFIG[env]['tarjeta_digital']['domain']
TDIGITAL_USER              = APP_CONFIG[env]['tarjeta_digital']['user']
TDIGITAL_PASSEWORD         = APP_CONFIG[env]['tarjeta_digital']['password']
TDIGITAL_RETAILER          = APP_CONFIG[env]['tarjeta_digital']['retailer']

TODOPAGO_APP_KEY            = APP_CONFIG[env]['todopago']['app_key']
TODOPAGO_MERCHANT           = APP_CONFIG[env]['todopago']['merchant']
TODOPAGO_SECURITY           = APP_CONFIG[env]['todopago']['security']
TODOPAGO_ENDPOINT           = APP_CONFIG[env]['todopago']['endpoint']
TODOPAGO_AUTHORIZE_URL      = APP_CONFIG[env]['todopago']['authorize_url']
TODOPAGO_HYBRID_JS          = APP_CONFIG[env]['todopago']['hybrid_js']

OCA_EPAK_USER               = APP_CONFIG[env]['oca_epak']['user']
OCA_EPAK_PASSWORD           = APP_CONFIG[env]['oca_epak']['password']
OCA_EPAK_ACCOUNT            = APP_CONFIG[env]['oca_epak']['account_number']
OCA_EPAK_CUIT               = APP_CONFIG[env]['oca_epak']['cuit']

ON_STAGING                  = APP_CONFIG[env]['on_staging']

PAPERCLIP                   = APP_CONFIG[env]['paperclip']

PICKIT_ENDPOINT             = APP_CONFIG[env]['pickit']['endpoint']
PICKIT_API_KEY              = APP_CONFIG[env]['pickit']['api_key']
PICKIT_TOKEN_ID             = APP_CONFIG[env]['pickit']['token_id']

REDIS_DB                    = APP_CONFIG[env]['redis']['db']
REDIS_HOST                  = APP_CONFIG[env]['redis']['host']
REDIS_PORT                  = APP_CONFIG[env]['redis']['port']
REDIS_URL                   = APP_CONFIG[env]['redis']['url']

SECRET_TOKEN                = APP_CONFIG[env]['secret_token']

SENDGRID                    = APP_CONFIG[env]['sendgrid']
POSTMARK                    = APP_CONFIG[env]['postmark']

SIDEKIQ_USER                = APP_CONFIG[env]['sidekiq']['user']
SIDEKIQ_PASSWORD            = APP_CONFIG[env]['sidekiq']['password']


SMART_CARRIER_API_KEY       = APP_CONFIG[env]['smart_carrier']['api_key']
SMART_CARRIER_URL           = APP_CONFIG[env]['smart_carrier']['url']

ROLLBAR_ACCESS_TOKEN        = APP_CONFIG[env]['rollbar']['access_token']

SSL_ENABLED                 = APP_CONFIG[env]['ssl_enabled']
SECURE_PROTOCOL             = SSL_ENABLED ? 'https' : 'http'

SHIPNOW                     = APP_CONFIG[env]['shipnow']

SHOPIFY                     = APP_CONFIG[env]['shopify']

TWITTER_API                 = APP_CONFIG[env]['twitter_api']
TWITTER_SECRET              = APP_CONFIG[env]['twitter_secret']

VEINTERACTIVE_ENABLED       = APP_CONFIG[env]['veinteractive']['enabled']
VEINTERACTIVE_TAG           = APP_CONFIG[env]['veinteractive']['tag']
VEINTERACTIVE_PIXEL         = APP_CONFIG[env]['veinteractive']['pixel']

STRING_SECURITY             = APP_CONFIG[env]['string']['security']

SPORTCLUB_ENDPOINT          = APP_CONFIG[env]['sportclub']['endpoint']
SPORTCLUB_TOKEN             = APP_CONFIG[env]['sportclub']['token']

VISAPUNTOS_DEFAULT_APIKEY   = APP_CONFIG[env]['visa_puntos']['default_api_key']
VISAPUNTOS_ENDPOINT         = APP_CONFIG[env]['visa_puntos']['endpoint']
VISAPUNTOS_COD_PREMIO	    	= APP_CONFIG[env]['visa_puntos']['cod_premio']

MACRO_LOGIN_ENDPOINT        = APP_CONFIG[env]['macro_login']['endpoint']
MACRO_LOGIN_API_KEY         = APP_CONFIG[env]['macro_login']['api_key']

TECHNISYS_URL               = APP_CONFIG[env]['technisys']['url']

PYP_RECARGA_URL             = APP_CONFIG[env]['pyp']['recarga_url']
PYP_CATALOG_URL             = APP_CONFIG[env]['pyp']['catalogo_url']
RECHARGE_CATALOG_ID         = APP_CONFIG[env]['pyp']['recarga_catalogo_id']
RECHARGE_API_KEY            = APP_CONFIG[env]['pyp']['recarga_api_key']
PRODUCT_CATALOG_ID          = APP_CONFIG[env]['pyp']['product_catalog_id']
PRODUCT_API_KEY             = APP_CONFIG[env]['pyp']['product_api_key']

KRABPACK_URL                = APP_CONFIG[env]['krabpack']['url']
KRABPACK_APIKEY             = APP_CONFIG[env]['krabpack']['api_key']
KRABPACK_MACRO_APIKEY       = APP_CONFIG[env]['krabpack']['macro_api_key']

AEROLINEAS_ARGENTINAS_URL   = APP_CONFIG[env]['aerolineas_argentinas']['url']

SUBE_URL                    = APP_CONFIG[env]['sube']['url']

BNA_SALT                    = APP_CONFIG[env]['bna']['salt']

NACION_TOKENS_URL           = APP_CONFIG[env]['nacion_tokens']['url']
NACION_TOKENS_API_KEY       = APP_CONFIG[env]['nacion_tokens']['api_key']

RENAPER_URL                 = APP_CONFIG[env]['renaper']['url']
RENAPER_URL_AUTH            = APP_CONFIG[env]['renaper']['url_auth']
RENAPER_GRANT_TYPE          = APP_CONFIG[env]['renaper']['grant_type']
RENAPER_CLIENT_ID           = APP_CONFIG[env]['renaper']['client_id']
RENAPER_USERNAME            = APP_CONFIG[env]['renaper']['username']
RENAPER_PASSWORD            = APP_CONFIG[env]['renaper']['password']

EMBLUE_TOKEN                = APP_CONFIG[env]['emblue']['auth_token']
EMBLUE_API_ENDPOINT         = APP_CONFIG[env]['emblue']['api_endpoint']
EMBLUE_TIME_LAPSE           = APP_CONFIG[env]['emblue']['time_lapse']

EMBLUE_MAILER_TOKEN         = APP_CONFIG[env]['emblue']['mailer']['auth_token']
EMBLUE_MAILER_API_ENDPOINT  = APP_CONFIG[env]['emblue']['mailer']['api_endpoint']

WEBHOOK_TOKEN               = APP_CONFIG[env]['rocketchat']['chat_token']

OCHENTA_API_ENDPOINT        = APP_CONFIG[env]['ochenta']['api_endpoint']
OCHENTA_SHOP_ID             = APP_CONFIG[env]['ochenta']['shop_id']
OCHENTA_CATEGORY_ID         = APP_CONFIG[env]['ochenta']['category_id']
OCHENTA_MANUFACTURER_ID     = APP_CONFIG[env]['ochenta']['manufacturer_id']
OCHENTA_USERNAME            = APP_CONFIG[env]['ochenta']['username']
OCHENTA_PASSWORD            = APP_CONFIG[env]['ochenta']['password']
OCHENTA_CLIENT_ID           = APP_CONFIG[env]['ochenta']['client_id']
OCHENTA_EXTERNAL_PROGRAM_ID = APP_CONFIG[env]['ochenta']['external_program_id']

DEFAULT_EXTERNAL_PROGRAM_ID = APP_CONFIG[env]['modo']['bna']['default_external_program_id']

BLISTER_API_ENDPOINT        = APP_CONFIG[env]['blister']['api_endpoint']
BLISTER_USERNAME            = APP_CONFIG[env]['blister']['username']
BLISTER_PASSWORD            = APP_CONFIG[env]['blister']['password']

BOSTON_API_ENDPOINT        = APP_CONFIG[env]['boston']['api_endpoint']
BOSTON_USERNAME            = APP_CONFIG[env]['boston']['username']
BOSTON_PASSWORD            = APP_CONFIG[env]['boston']['password']

ROCKETCHAT_PLATFORM_TOKEN   = APP_CONFIG[env]['rocketchat']['platform-token']
ROCKETCHAT_PAYMENTS_TOKEN    = APP_CONFIG[env]['rocketchat']['payments-token']
ROCKETCHAT_BLISTER_TOKEN    = APP_CONFIG[env]['rocketchat']['blister-token']
ROCKETCHAT_MATRIX_TOKEN     = APP_CONFIG[env]['rocketchat']['matrix-token']
AVENIDA_PAYMENTS_URL        = APP_CONFIG[env]['avenida-payments']['url']
MERCADOPAGO_TOKEN_ENDPOINT  = APP_CONFIG[env]['avenida-payments']['mercadopago']['url']

MAX_PAYMENT_CARDS = 2

PENDING_PAYMENTS_AS_CONFIRMED = APP_CONFIG[env].dig(:pending_payments_update_stock) || false
API_DELETE_PRODUCT_ATTRIBUTES = false

CHECK_POINTS = APP_CONFIG[env]['bna_loyalty']['check_points']
BNA_LOYALTY_BASE_URL = APP_CONFIG[env]['bna_loyalty']['base_url']
BNA_LOYALTY_SSO_URL = APP_CONFIG[env]['bna_loyalty']['sso_url']
BNA_LOYALTY_SSO_CLIENT_ID = APP_CONFIG[env]['bna_loyalty']['sso_client_id']
BNA_LOYALTY_SSO_CLIENT_SECRET = APP_CONFIG[env]['bna_loyalty']['sso_client_secret']
BNA_LOYALTY_SSO_SCOPE = APP_CONFIG[env]['bna_loyalty']['sso_scope']
BNA_LOYALTY_SSO_TOKEN_ENDPOINT = APP_CONFIG[env]['bna_loyalty']['sso_token_endpoint']
BNA_LOYALTY_SSO_USERNAME = APP_CONFIG[env]['bna_loyalty']['sso_username']
BNA_LOYALTY_SSO_PASSWORD = APP_CONFIG[env]['bna_loyalty']['sso_password']
BNA_LOYALTY_POINTS_MONEY = APP_CONFIG[env]['bna_loyalty']['points_money']
BNA_LOYALTY_POINTS_OVER_PERC = 1 + (APP_CONFIG.dig(env, 'bna_loyalty', 'max_over_points_perc') || 5) / 100.0

DISABLE_SQL_LOGS = APP_CONFIG[env]['disable_sql_logs']
SENTRY_DSN = APP_CONFIG[env]['sentry_dsn']


MIN_LOAN_AMOUNT = APP_CONFIG[env]['loan']['min_amount']
COYOTE_URL_PRESTAMOS = APP_CONFIG[env]['loan']['url_coyote']
ENCRYPT_KEY = APP_CONFIG[env]['encrypt_key']

QR_CLIENT_ID = APP_CONFIG[env]['qr']['qr_client_id']
QR_CLIENT_SECRET = APP_CONFIG[env]['qr']['qr_client_secret']
QR_SECKEY = APP_CONFIG[env]['qr']['qr_seckey']
QR_PROVISION_KEY = APP_CONFIG[env]['qr']['qr_provision_key']
QR_API_KEY = APP_CONFIG[env]['qr']['qr_apikey']
QR_BASE_URL = APP_CONFIG[env]['qr']['qr_base_url']
QR_BASE_URL_QR_COMPONENT = APP_CONFIG[env]['qr']['qr_base_url_qrcomponent']
QR_JWT = APP_CONFIG[env]['qr']['jwt']
QR_ISS = APP_CONFIG[env]['qr']['iss']
QR_SIGNATURE = APP_CONFIG[env]['qr']['signature']
QR_RESPONSE_INF = APP_CONFIG[env]['qr']['response_inf']
QR_ID_APP = APP_CONFIG[env]['qr']['qr_id_app']
QR_COUNTRY = APP_CONFIG[env]['qr']['qr_country']
QR_IDMAQC_SERVICE = APP_CONFIG[env]['qr']['qr_idmaqc_service']
QR_PROFILE_SERVICES = APP_CONFIG[env]['qr']['qr_profile_services']
QR_SERVICES = APP_CONFIG[env]['qr']['qr_services']
QR_FRONT_DECISION_BASE_URL = APP_CONFIG[env]['qr']['qr_front_decision_base_url']
QR_SESSION_ID_AUTH = APP_CONFIG[env]['qr']['qr_session_id_auth']

PRICES_WITHOUT_TAXES_ENABLED = APP_CONFIG[env]['prices_without_taxes_enabled'] || false
PRICES_WITHOUT_TAXES_MANDATORY = APP_CONFIG[env]['prices_without_taxes_mandatory'] || false
PRICES_WITHOUT_TAXES_AUTO_CALCULATION = APP_CONFIG[env]['prices_without_taxes_auto_calculation'] || false

MOTO_CATEGORY_ID = APP_CONFIG[env]['mobility']['moto_category_id']
